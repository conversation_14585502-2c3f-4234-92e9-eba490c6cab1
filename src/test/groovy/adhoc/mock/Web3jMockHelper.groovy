package adhoc.mock

import org.web3j.protocol.websocket.events.NewHead
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams
import spock.lang.Specification

/**
 * Helper class for setting up Web3j mock configurations used in testing.
 * This class centralizes Web3j mock setup logic, improving maintainability
 * and reusability across test classes.
 *
 * Note: This class is designed to be used within Spock test classes where Mock() is available.
 */
class Web3jMock<PERSON>elper extends Specification {


	/**
	 * Creates mock NewHeadsNotifications for testing with flexible parameters
	 * @param startBlockNumber The starting block number in decimal format
	 * @param numberOfNotifications The number of NewHeadsNotification objects to create
	 * @param mockCreator Closure that creates Mock objects (typically from test class)
	 * @return List of mock NewHeadsNotification objects with sequential block numbers
	 */
	def createMockNewHeadsNotifications(long startBlockNumber, int numberOfNotifications, Closure mockCreator) {
		def notifications = []

		for (int i = 0; i < numberOfNotifications; i++) {
			def currentBlockNumber = startBlockNumber + i
			def hexBlockNumber = "0x" + Long.toHexString(currentBlockNumber)

			def notification = mockCreator(NewHeadsNotification)
			def params = mockCreator(NotificationParams)
			def result = mockCreator(NewHead)
			result.getNumber() >> hexBlockNumber

			notification.getParams() >> params
			params.getResult() >> result
			notifications.add(notification)
		}

		return notifications
	}
}
